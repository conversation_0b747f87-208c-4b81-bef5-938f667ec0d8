{"deno.enable": true, "deno.lint": false, "deno.unstable": false, "deno.config": "deno.jsonc", "biome.enabled": true, "[css][tailwindcss]": {"editor.defaultFormatter": "biomejs.biome"}, "editor.codeActionsOnSave": {"source.organizeImports.biome": "explicit", "source.fixAll.biome": "explicit", "source.removeUnusedImports": "always"}, "files.associations": {"*.ts": "typescript", "*.tsx": "typescriptreact", "*.css": "tailwindcss"}, "editor.defaultFormatter": "biomejs.biome", "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "tailwindCSS.classFunctions": ["cva", "cx"]}