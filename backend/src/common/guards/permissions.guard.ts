import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import type { PermissionsService } from '../../permissions/services/permissions.service.ts';
import { PERMISSIONS_KEY, PermissionRequirement } from '../decorators/permissions.decorator.ts';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private permissionsService: PermissionsService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.getAllAndOverride<PermissionRequirement[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredPermissions) {
      return true; // No permissions required
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    // Check each required permission
    for (const permission of requiredPermissions) {
      const resourceId = permission.resourceIdParam 
        ? request.params[permission.resourceIdParam] 
        : 'default';

      const hasPermission = await this.permissionsService.checkPermission(
        user.id,
        permission.resource,
        resourceId,
        permission.action,
      );

      if (!hasPermission) {
        throw new ForbiddenException(
          `Insufficient permissions: ${permission.action} on ${permission.resource}:${resourceId}`,
        );
      }
    }

    return true;
  }
}
